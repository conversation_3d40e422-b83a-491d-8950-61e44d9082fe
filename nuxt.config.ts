import tailwindcss from '@tailwindcss/vite'

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: false },
  debug: false,

  future: {
    compatibilityVersion: 4,
  },
  
  modules: [
    '@vueuse/nuxt',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/color-mode',
    '@nuxt/eslint',
    'nuxt-auth-utils',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/fonts',
  ],

  // * Note that this option will not override the default directories (~/components, ~/composables, ~/middleware, ~/utils).
  imports: {
    dirs: [
      '../stores',
      '../app/utils',
    ],
  },

  fonts: {
    providers: {
      google: false,
      googleicons: false,
    },
    priority: ['bunny', 'fontsource', 'local']
  },

  // vue: {
  //   compilerOptions: {
  //     isCustomElement: tag => ['iconify-icon'].includes(tag),
  //   },
  // },

  colorMode: {
    preference: 'dark', // default value of $colorMode.preference -> 'system'
    fallback: 'dark', // fallback value if not system preference found
    classSuffix: '',
  },

  // content: {
  //   // highlight: {
  //   //   // Theme used in all color schemes.
  //   //   theme: 'github-dark-high-contrast',
  //   //   // OR
  //   //   // theme: {
  //   //   //   // Default theme (same as single string)
  //   //   //   default: 'vitesse-dark',
  //   //   //   // Theme used if `html.dark`
  //   //   //   dark: 'github-dark-high-contrast',
  //   //   //   // Theme used if `html.sepia`
  //   //   //   sepia: 'monokai'
  //   //   // }
  //   // },
  // },

  runtimeConfig: {
    fromEmail: 'gomark.pro <<EMAIL>>',
    public: {
      brand: 'Gomark',
      contactEmail: '<EMAIL>',
      baseUrl: process.env.NODE_ENV === 'development' ? `http://localhost:${process.env.PORT ?? 3000}` : 'https://gomark.pro',
    },
  },

  // Build as SPA application
  // ssr: false,

  routeRules: {
    '/admin/**': { ssr: false },
    '/dashboard/**': { ssr: false },
    // '/api/**': { cors: true },
  },

  // hmr 的性能取决于 tailwindcss 的大小，而大小取决于页面使用 class 的多少以及 theme 的多少
  // 参考 nuxt-app，删除 ui 文件夹及 theme 等等，默认的 tailwindcss 仅 40 KB左右，hmr 就非常快
  css: ['~/assets/css/tailwind.css'],
  vite: {
    plugins: [
      tailwindcss(),
    ],
  },

  eslint: {
    config: {
      stylistic: true,
    },
  },

  icon: {
    mode: 'svg',
    customCollections: [
      {
        prefix: 'custom',
        dir: './app/assets/custom-icons'
      },
    ],
  },
  
  shadcn: {
    prefix: '',
    componentDir: '@/components/ui',
  },
})
